import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Card from '../../components/common/card/Card';
import ProjectDialog from '../../components/common/ProjectDialog/ProjectDialog';
import { PredefinedFile } from '../../types/predefinedFiles';
import { PredefinedPrompt } from '../../types/predefinedPrompts';
import styles from './Library.module.css';
import { useUploadFileMutation } from '../../services/chatServices';
import { getSimplifiedFileExtension } from '../../utils/getFileExtention';
import { createFormData } from '../../utils/formDataHelper';
import useLocalStorage from '../../hooks/useLocalStorage';
import toast from 'react-hot-toast';
import { useGetDemoFilesQuery } from '../../services';

// Dummy users for demonstration
const users = [
  { name: 'AI Model', avatar: 'https://i.pravatar.cc/150?img=1' },
  { name: 'System', avatar: 'https://i.pravatar.cc/150?img=2' },
];

// Helper function to get MIME type from file extension
const getMimeTypeFromExtension = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const mimeTypes: { [key: string]: string } = {
    pdf: 'application/pdf',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    xls: 'application/vnd.ms-excel',
    csv: 'text/csv',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    doc: 'application/msword',
    txt: 'text/plain',
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
  };
  return mimeTypes[extension || ''] || 'application/octet-stream';
};

// Helper function to get file category from extension
const getCategoryFromExtension = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const categories: { [key: string]: string } = {
    pdf: 'PDF Documents',
    xlsx: 'Spreadsheets',
    xls: 'Spreadsheets',
    csv: 'Data Files',
    docx: 'Word Documents',
    doc: 'Word Documents',
    txt: 'Text Files',
    png: 'Images',
    jpg: 'Images',
    jpeg: 'Images',
  };
  return categories[extension || ''] || 'Other Files';
};

// Helper function to generate suggested questions based on file type
const getSuggestedQuestions = (fileName: string): string[] => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const baseQuestions = [
    'What is the main content of this document?',
    'Can you summarize the key points?',
    'What are the important details I should know?',
    'Extract the most relevant information from this file.',
  ];

  const specificQuestions: { [key: string]: string[] } = {
    pdf: [
      'Summarize the main sections of this document',
      'What are the key findings or conclusions?',
      'Extract important data or statistics',
      'What recommendations are provided?',
    ],
    xlsx: [
      'What data is contained in this spreadsheet?',
      'Analyze the numerical trends',
      'What are the key metrics or KPIs?',
      'Summarize the data patterns',
    ],
    csv: [
      'What columns and data types are in this dataset?',
      'Analyze the data distribution',
      'What insights can be derived from this data?',
      'Are there any data quality issues?',
    ],
  };

  return specificQuestions[extension || ''] || baseQuestions;
};

// Transform API file data to PredefinedFile format
const transformApiFilesToPredefinedFiles = (
  apiFiles: any[]
): PredefinedFile[] => {
  if (!apiFiles || !Array.isArray(apiFiles)) return [];

  return apiFiles.map((file, index) => {
    const fileName = file.fileName || '';
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';

    return {
      id: `api-file-${index}`,
      name: fileName,
      description: `Real document from the system: ${fileName}`,
      category: getCategoryFromExtension(fileName),
      fileType: fileExtension,
      mimeType: getMimeTypeFromExtension(fileName),
      size: 'Unknown', // API doesn't provide size
      pageCount: fileExtension === 'csv' ? 1 : undefined,
      lastModified: 'Recently added',
      tags: [fileExtension, getCategoryFromExtension(fileName).toLowerCase()],
      suggestedQuestions: getSuggestedQuestions(fileName),
      hasContext: false, // These are real files, not pre-processed
      contextDescription: `Real file from system: ${file.fullPath}`,
      fullPath: file.fullPath, // Store the full path for reference
    } as PredefinedFile;
  });
};

const Library = () => {
  const [user] = useLocalStorage('user', null);

  const storedUser = JSON.parse(localStorage.getItem('user') || '{}');

  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPredefinedFile, setSelectedPredefinedFile] =
    useState<PredefinedFile | null>(null);
  const [selectedPrompt, setSelectedPrompt] = useState<PredefinedPrompt | null>(
    null
  );
  const [title, setTitle] = useState('');
  const [rangeInput, setRangeInput] = useState('');
  const [uploadFile] = useUploadFileMutation();
  const [localstorage] = useLocalStorage('user', []);
  const [loading, setLoading] = useState(false);

  const { data: getDemoFiles, isLoading: isLoadingDemoFiles } =
    useGetDemoFilesQuery();

  // Transform API data to predefined files format
  const predefinedFiles = useMemo(() => {
    return transformApiFilesToPredefinedFiles(getDemoFiles);
  }, [getDemoFiles]);

  // Create a mock File object from predefined file data
  const createMockFile = (predefinedFile: PredefinedFile): File => {
    const blob = new Blob(['Mock file content for ' + predefinedFile.name], {
      type: predefinedFile.mimeType,
    });
    const file = new File([blob], predefinedFile.name, {
      type: predefinedFile.mimeType,
      lastModified: Date.now(),
    });
    return file;
  };

  const handleCardClick = (predefinedFile: PredefinedFile) => {
    setSelectedPredefinedFile(predefinedFile);
    console.log(predefinedFile.name, 'hello');
    setTitle(predefinedFile.name.replace(/\.[^/.]+$/, '')); // Remove file extension
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
    setSelectedPredefinedFile(null);
    setSelectedPrompt(null);
    setTitle('');
    setRangeInput('');
  };

  const handleProjectSelection = async (
    projectId: string,
    pageRanges: string[],
    promptText: string,
    _file: File,
    _promptId: string
  ) => {
    try {
      if (!selectedPredefinedFile) {
        throw new Error('No predefined file selected');
      }

      if (!projectId) {
        throw new Error('Project ID is required');
      }

      if (!storedUser?.id) {
        throw new Error('User ID is required');
      }

      setLoading(true);

      // For predefined files, we simulate the upload process
      // In a real implementation, you might want to fetch the actual file content
      // or use a different API endpoint for predefined files

      const mockFile = createMockFile(selectedPredefinedFile);
      const numberOfPages = selectedPredefinedFile.pageCount || 1;
      const fileExtension = getSimplifiedFileExtension(mockFile);

      if (!fileExtension) {
        throw new Error('Invalid file type');
      }

      // Create form data for library file upload
      const formData = createFormData({
        userId: storedUser?.id,
        projectId,
        pageNumber: rangeInput || '',
        fileName: '',
        fileType: '',
        prompt: ' ',
        library_filename: selectedPredefinedFile.name,
        response_type: 'Brief',
      });

      const response: any = await uploadFile({ formData }).unwrap();

      toast.success(
        response.status === 200
          ? 'Document loaded successfully!'
          : 'Predefined file loaded successfully!',
        { duration: 6000 }
      );

      // Navigate to chat with the predefined file context
      navigate('/chat', {
        state: {
          summary: response.summary || selectedPredefinedFile.description,
          tables: response.tables || [],
          questions:
            response.suggested_questions ||
            selectedPredefinedFile.suggestedQuestions,
          file: mockFile,
          numberOfPages,
          insightProjectId: response.object_id,
          projectId,
          pageRange: pageRanges,
          uploadFileTitle: title,
          fileUpload: true,
          isPredefinedFile: true,
          predefinedFileData: selectedPredefinedFile,
        },
      });

      handleDialogClose();
    } catch (error) {
      console.error('Predefined file loading failed:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to load predefined file',
        { duration: 6000 }
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.libraryContainer}>
      <div className={styles.header}>
        <h1 className={styles.title}>Document Library</h1>
        {/* <p className={styles.subtitle}>
          Select from available documents in the system. These files are ready
          for analysis and processing.
        </p> */}
      </div>

      <div className={styles.content}>
        {isLoadingDemoFiles ? (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>Loading documents...</p>
          </div>
        ) : predefinedFiles.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>No documents available at the moment.</p>
          </div>
        ) : (
          <Grid container spacing={3}>
            {predefinedFiles.map((file: PredefinedFile) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={file.id}>
                <Card
                  type={'library'}
                  title={file.name}
                  users={users}
                  timeAgo={''}
                  status="Completed"
                  role="creator"
                  initials={file.name.slice(0, 2).toUpperCase()}
                  handleCardClick={() => handleCardClick(file)}
                  fromProject={false}
                />
              </Grid>
            ))}
          </Grid>
        )}
      </div>

      {openDialog && selectedPredefinedFile && (
        <ProjectDialog
          onClose={handleDialogClose}
          onSelectProject={handleProjectSelection}
          rangeInput={rangeInput}
          setRangeInput={setRangeInput}
          title={title}
          setTitle={setTitle}
          selectedPrompt={selectedPrompt}
          uploadedFile={createMockFile(selectedPredefinedFile)}
          isLoading={loading}
        />
      )}
    </div>
  );
};

export default Library;
